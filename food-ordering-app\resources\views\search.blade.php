@extends('layouts.app')

@section('title', 'Search Results - Food Ordering App')

@push('styles')
<style>
    /* Advanced Search Page Styles - Real Food App Design */

    /* Root Variables for Consistent Design */
    :root {
        --primary-orange: #ff6b35;
        --primary-orange-light: #ff8c5a;
        --primary-orange-dark: #e55a2b;
        --secondary-blue: #4f46e5;
        --success-green: #10b981;
        --warning-yellow: #f59e0b;
        --danger-red: #ef4444;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --border-radius: 12px;
        --border-radius-lg: 16px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Search page specific styles */
    .search-page-container {
        padding-left: 0 !important;
        margin-left: 0 !important;
        background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
        min-height: 100vh;
    }

    /* Override container padding for search page */
    .search-page .container-mobile {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Enhanced main content with proper spacing from sidebar */
    .search-main-content {
        transition: var(--transition);
        background: transparent;
        position: relative;
        z-index: 10;
    }

    /* Responsive sidebar spacing - ensuring products are never hidden */
    @media (max-width: 767px) {
        .search-main-content {
            margin-left: calc(16.666667vw + 1rem) !important;
            padding-right: 0.5rem;
        }

        /* Ensure minimum spacing on very small screens */
        @media (max-width: 480px) {
            .search-main-content {
                margin-left: calc(120px + 1rem) !important;
            }
        }
    }

    @media (min-width: 768px) and (max-width: 1023px) {
        .search-main-content {
            margin-left: calc(16.666667vw + 1.5rem) !important;
            padding-right: 1rem;
        }
    }

    @media (min-width: 1024px) {
        .search-main-content {
            margin-left: calc(16.666667vw + 2rem) !important;
            padding-right: 1.5rem;
        }
    }

    /* Ensure sticky footer is always visible with enhanced styling */
    body.search-page {
        padding-bottom: 90px !important;
        background: var(--gray-50);
    }

    /* Advanced Product Grid with Real Food App Styling */
    .search-products-grid {
        padding: 1rem 0.5rem;
        gap: 1rem;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    @media (max-width: 767px) {
        .search-products-grid {
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 0.75rem;
            padding: 0.75rem 0.25rem;
        }
    }

    @media (min-width: 768px) and (max-width: 1023px) {
        .search-products-grid {
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 1rem;
        }
    }

    @media (min-width: 1024px) {
        .search-products-grid {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1.25rem;
            padding: 1.25rem 0.75rem;
        }
    }

    /* Enhanced Food Cards with Modern Design */
    .food-card {
        background: #ffffff;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
        overflow: hidden;
        position: relative;
        border: 1px solid var(--gray-100);
    }

    .food-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-orange);
    }

    .food-card img {
        transition: var(--transition);
    }

    .food-card:hover img {
        transform: scale(1.05);
    }

    /* Modern Filter Badges */
    .filter-badge {
        background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-orange-light) 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
    }

    .filter-badge:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    /* Enhanced Quick Filters Slider - Sticky Version */
    .quick-filters-container-sticky {
        background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
        border-bottom: 1px solid var(--gray-200);
        box-shadow: var(--shadow-md);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: var(--transition);
        position: sticky;
        margin: 0;
        padding: 0;
        z-index: 40;
    }

    .quick-filters-container-sticky:hover {
        box-shadow: var(--shadow-lg);
        border-bottom-color: var(--primary-orange);
        border-top-color: var(--primary-orange);
    }

    /* Remove any gaps between navigation and slider */
    .search-page-wrapper {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure no gaps in main content area */
    .search-page main {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Remove default spacing from container */
    .search-page .container-mobile {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }

    /* Ensure seamless connection */
    .quick-filters-container-sticky {
        margin-top: -1px !important; /* Overlap border to remove gap */
    }

    /* Mobile-specific aggressive gap removal */
    @media (max-width: 768px) {
        /* Force zero spacing on all elements */
        body {
            margin: 0 !important;
            padding-bottom: 80px !important; /* Only keep bottom padding for footer */
        }

        header {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        main {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        .search-page-wrapper {
            margin: 0 !important;
            padding: 0 !important;
        }

        .quick-filters-container-sticky {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
            padding-top: 0 !important;
            border-top: none !important;
            position: sticky;
            top: var(--header-height, 120px); /* Dynamic header height */
        }

        /* Remove any default browser margins */
        * {
            box-sizing: border-box;
        }
    }

    .filter-btn {
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-200);
        border-radius: 25px;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: var(--transition);
        white-space: nowrap;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
    }

    .filter-btn:hover {
        background: var(--primary-orange);
        color: white;
        border-color: var(--primary-orange);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    .filter-btn.active {
        background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-orange-light) 100%);
        color: white;
        border-color: var(--primary-orange);
        box-shadow: var(--shadow-md);
    }

    /* Advanced Sidebar Styling */
    .modern-sidebar {
        box-shadow: var(--shadow-xl);
        border-right: 1px solid var(--gray-200);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .sidebar-header {
        background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
        border-bottom: 1px solid var(--gray-100);
    }

    .sidebar-content {
        background: #ffffff;
    }

    .sidebar-content::-webkit-scrollbar {
        width: 4px;
    }

    .sidebar-content::-webkit-scrollbar-track {
        background: var(--gray-100);
        border-radius: 2px;
    }

    .sidebar-content::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: 2px;
        transition: var(--transition);
    }

    .sidebar-content::-webkit-scrollbar-thumb:hover {
        background: var(--primary-orange);
    }

    /* Filter Section Styling */
    .filter-section {
        background: #ffffff;
        transition: var(--transition);
    }

    .filter-section:hover {
        background: var(--gray-50);
    }

    /* Filter Option Styling */
    .filter-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem;
        border-radius: var(--border-radius);
        color: var(--gray-700);
        font-size: 0.875rem;
        font-weight: 500;
        transition: var(--transition);
        border: 1px solid transparent;
        text-decoration: none;
    }

    .filter-option:hover {
        background: var(--gray-100);
        color: var(--gray-800);
        border-color: var(--gray-200);
        transform: translateX(2px);
    }

    .filter-option.active {
        background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-orange-light) 100%);
        color: white;
        border-color: var(--primary-orange);
        box-shadow: var(--shadow-sm);
    }

    .filter-option.active:hover {
        transform: translateX(2px);
        box-shadow: var(--shadow-md);
    }

    /* Scrollbar Hide Utility */
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Enhanced Page Header */
    .page-header {
        background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
        border-radius: var(--border-radius-lg);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-100);
    }

    .page-title {
        font-size: 1.875rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-700) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Active Filter Badges */
    .active-filter-badge {
        background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-orange-light) 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.875rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
    }

    .active-filter-badge:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    .active-filter-badge .remove-filter {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 1.25rem;
        height: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        transition: var(--transition);
    }

    .active-filter-badge .remove-filter:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    /* Modern Tab Styling */
    .modern-tab {
        position: relative;
        transition: var(--transition);
    }

    .modern-tab:hover {
        background: var(--gray-50);
    }

    .modern-tab.active {
        background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-orange-light) 100%);
        color: white;
        border-color: var(--primary-orange);
    }

    .modern-tab.active:hover {
        background: linear-gradient(135deg, var(--primary-orange-dark) 0%, var(--primary-orange) 100%);
    }

    /* Results Summary Styling */
    .results-summary {
        background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
        border: 1px solid var(--gray-200);
        transition: var(--transition);
    }

    .results-summary:hover {
        box-shadow: var(--shadow-md);
        border-color: var(--primary-orange);
    }

    /* Enhanced Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    /* Enhanced Empty State */
    .empty-state {
        background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
        border: 2px dashed var(--gray-300);
        border-radius: var(--border-radius-lg);
        padding: 3rem;
        text-align: center;
        transition: var(--transition);
    }

    .empty-state:hover {
        border-color: var(--primary-orange);
        background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
    }

    .empty-state-icon {
        font-size: 3rem;
        color: var(--gray-400);
        margin-bottom: 1rem;
        transition: var(--transition);
    }

    .empty-state:hover .empty-state-icon {
        color: var(--primary-orange);
        transform: scale(1.1);
    }

    /* Responsive Enhancements */
    @media (max-width: 640px) {
        .page-header {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .active-filter-badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        .modern-tab {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
        }

        .results-summary {
            padding: 1rem;
        }
    }

    /* Layout with Filter Nav Header and Sticky Quick Slider */
    .food-items-container {
        padding-top: 1rem;
        min-height: 100vh;
        margin-top: 80px; /* Account for sticky quick slider height */
    }

    /* Optimized spacing for items with sticky slider */
    .search-main-content {
        padding-top: 0;
    }

    /* Ensure sticky slider doesn't interfere with sidebar */
    .quick-filters-container {
        margin-left: 0;
        padding-left: 0;
    }

    /* Enhanced item cards for better visibility */
    .food-card {
        transition: var(--transition);
        cursor: pointer;
    }

    .food-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    /* Better mobile spacing for items-only view */
    @media (max-width: 767px) {
        .food-items-container {
            padding-top: 0.5rem;
        }

        .search-products-grid {
            padding: 0.5rem 0.25rem;
        }

        /* Mobile-specific gap removal */
        .search-page-wrapper {
            margin: 0 !important;
            padding: 0 !important;
        }

        .quick-filters-container-sticky {
            margin-top: 0 !important;
            border-top: none !important;
            padding-top: 0;
        }

        .quick-filters-container-sticky .container-mobile {
            padding-top: 0;
            margin-top: 0;
        }
    }

    /* Extra mobile gap removal for very small screens */
    @media (max-width: 480px) {
        .search-page-wrapper {
            margin: 0 !important;
            padding: 0 !important;
        }

        .quick-filters-container-sticky {
            margin-top: 0 !important;
            padding-top: 0 !important;
            border-top: none !important;
        }

        .quick-filters-container-sticky .py-4 {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }

        /* Force remove any default margins on mobile */
        main {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        /* Remove any container margins */
        .container-mobile {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
    }

    /* Advanced Filter Navigation Bar */
    .filter-nav-container {
        background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
        border-bottom: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        position: sticky;
        top: calc(4rem + 80px); /* Below main nav and quick filters */
        z-index: 35;
        transition: var(--transition);
    }

    .filter-nav-container:hover {
        box-shadow: var(--shadow-md);
    }

    /* Filter Rows Container */
    .filter-rows {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        padding: 1rem 0;
    }

    .filter-row {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .filter-row-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--gray-700);
        min-width: 80px;
        display: flex;
        align-items: center;
        gap: 0.375rem;
    }

    /* Premium Filter Buttons (First Row) */
    .premium-filter-btn {
        background: linear-gradient(135deg, var(--gray-100) 0%, #ffffff 100%);
        color: var(--gray-700);
        border: 2px solid var(--gray-200);
        border-radius: 25px;
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
        font-weight: 600;
        transition: var(--transition);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: var(--shadow-sm);
    }

    .premium-filter-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s;
    }

    .premium-filter-btn:hover::before {
        left: 100%;
    }

    .premium-filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-orange);
    }

    .premium-filter-btn.premium {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #92400e;
        border-color: #fbbf24;
    }

    .premium-filter-btn.premium:hover {
        background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    }

    .premium-filter-btn.classic {
        background: linear-gradient(135deg, var(--secondary-blue) 0%, #6366f1 100%);
        color: white;
        border-color: var(--secondary-blue);
    }

    .premium-filter-btn.classic:hover {
        background: linear-gradient(135deg, #6366f1 0%, var(--secondary-blue) 100%);
        box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
    }

    .premium-filter-btn.delux {
        background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        color: white;
        border-color: #8b5cf6;
    }

    .premium-filter-btn.delux:hover {
        background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
        box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
    }

    .premium-filter-btn.active {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    /* Diet Filter Buttons (Second Row) */
    .diet-filter-btn {
        background: linear-gradient(135deg, var(--gray-100) 0%, #ffffff 100%);
        color: var(--gray-700);
        border: 2px solid var(--gray-200);
        border-radius: 20px;
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        transition: var(--transition);
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: var(--shadow-sm);
    }

    .diet-filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .diet-filter-btn.veg {
        background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
        color: white;
        border-color: var(--success-green);
    }

    .diet-filter-btn.veg:hover {
        background: linear-gradient(135deg, #059669 0%, var(--success-green) 100%);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    .diet-filter-btn.non-veg {
        background: linear-gradient(135deg, var(--danger-red) 0%, #dc2626 100%);
        color: white;
        border-color: var(--danger-red);
    }

    .diet-filter-btn.non-veg:hover {
        background: linear-gradient(135deg, #dc2626 0%, var(--danger-red) 100%);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    }

    .diet-filter-btn.active {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-width: 3px;
    }

    /* Filter Icons */
    .filter-icon {
        font-size: 1rem;
        margin-right: 0.25rem;
    }

    /* Responsive Design for Filter Nav */
    @media (max-width: 768px) {
        .filter-nav-container {
            top: calc(4rem + 70px); /* Adjust for mobile */
        }

        .filter-rows {
            padding: 0.75rem 0;
            gap: 0.5rem;
        }

        .filter-row {
            gap: 0.5rem;
        }

        .filter-row-label {
            font-size: 0.75rem;
            min-width: 60px;
        }

        .premium-filter-btn {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        .diet-filter-btn {
            padding: 0.625rem 1.25rem;
            font-size: 0.75rem;
        }
    }

    @media (max-width: 480px) {
        .filter-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .filter-row-label {
            min-width: auto;
        }

        .premium-filter-btn,
        .diet-filter-btn {
            flex: 1;
            min-width: 120px;
            justify-content: center;
        }
    }
</style>
@endpush

@section('content')
<div class="search-page-wrapper" style="margin-top: 0; padding-top: 0;">
<!-- Enhanced Quick Filters Slider (sticky directly below filter nav bar) -->
<div id="quick-filters-slider" class="quick-filters-container-sticky sticky z-40" style="top: 0; margin-top: 0;">
    <div class="container-mobile">
        <div class="py-4">
            <div class="flex space-x-3 overflow-x-auto pb-2 scrollbar-hide">
                <!-- All Categories -->
                <a href="{{ route('search') }}" class="filter-btn {{ !$categoryId ? 'active' : '' }}">
                    <i class="fas fa-utensils"></i>
                    <span>All Categories</span>
                </a>

                <!-- Dynamic Main Categories -->
                @foreach($mainCategories as $category)
                    <a href="{{ route('search', array_merge(request()->query(), ['category' => $category->id, 'subcategory' => null])) }}"
                       class="filter-btn category-filter {{ $categoryId == $category->id ? 'active' : '' }}"
                       data-category-id="{{ $category->id }}">
                        @if($category->image)
                            <img src="{{ $category->image }}" alt="{{ $category->name }}" class="w-4 h-4 rounded-full object-cover">
                        @else
                            <i class="fas fa-utensils"></i>
                        @endif
                        <span>{{ $category->name }}</span>
                    </a>
                @endforeach

                <!-- Quick Filters -->
                <a href="{{ route('search', ['vegetarian' => 1]) }}" class="filter-btn {{ $isVegetarian ? 'active' : '' }}" style="--filter-color: var(--success-green);">
                    <i class="fas fa-leaf"></i>
                    <span>Vegetarian</span>
                </a>
                <a href="{{ route('search', ['popular' => 1]) }}" class="filter-btn {{ $isPopular ? 'active' : '' }}" style="--filter-color: var(--warning-yellow);">
                    <i class="fas fa-star"></i>
                    <span>Popular</span>
                </a>
            </div>
        </div>
    </div>
</div>



<!-- Advanced Left Sidebar Filter (Real Food App Design) -->
<div id="filter-sidebar" class="modern-sidebar fixed left-0 bg-white z-30 transform transition-transform duration-300 ease-in-out" style="top: calc(120px + 80px); height: calc(100vh - 120px - 80px);">
    <!-- Sidebar Header with Gradient -->
    <div class="sidebar-header">
        <div class="relative z-10 p-3">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-filter text-white text-sm"></i>
                </div>
                <div>
                    <h3 class="font-bold text-sm text-gray-800">Filters</h3>
                    <p class="text-gray-500 text-xs hidden sm:block">Find your perfect meal</p>
                </div>
            </div>
        </div>
        <div class="h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>
    </div>

    <!-- Sidebar Content with Enhanced Scrolling -->
    <div class="sidebar-content overflow-y-auto h-full pb-20">

        <!-- Categories Section with Modern Design -->
        @if($mainCategories->count() > 0)
        <div class="filter-section p-3 border-b border-gray-100">
            <div class="flex items-center justify-between mb-3">
                <h4 class="font-semibold text-gray-800 text-sm flex items-center">
                    <i class="fas fa-th-large text-orange-500 mr-2"></i>
                    Categories
                </h4>
                <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{{ $mainCategories->count() }}</span>
            </div>
            <div class="space-y-1">
                <a href="{{ route('search', array_diff_key(request()->query(), ['category' => '', 'subcategory' => ''])) }}"
                   class="filter-option {{ !$categoryId ? 'active' : '' }}">
                    <div class="flex items-center">
                        <i class="fas fa-utensils text-gray-400 mr-2 w-4"></i>
                        <span>All Categories</span>
                    </div>
                    @if(!$categoryId)
                        <i class="fas fa-check text-orange-500"></i>
                    @endif
                </a>

                @foreach($mainCategories as $category)
                    <a href="{{ route('search', array_merge(request()->query(), ['category' => $category->id, 'subcategory' => null])) }}"
                       class="filter-option {{ $categoryId == $category->id ? 'active' : '' }}">
                        <div class="flex items-center">
                            @if($category->image)
                                <img src="{{ $category->image }}" alt="{{ $category->name }}" class="w-4 h-4 rounded-full object-cover mr-2">
                            @else
                                <i class="fas fa-utensils text-gray-400 mr-2 w-4"></i>
                            @endif
                            <span>{{ $category->name }}</span>
                        </div>
                        @if($categoryId == $category->id)
                            @if($subcategories->count() > 0)
                                <i class="fas fa-chevron-down text-orange-500"></i>
                            @else
                                <i class="fas fa-check text-orange-500"></i>
                            @endif
                        @endif
                    </a>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Subcategories Section with Modern Design -->
        @if($subcategories->count() > 0)
        <div class="filter-section p-3 border-b border-gray-100">
            <div class="flex items-center justify-between mb-3">
                <h4 class="font-semibold text-gray-800 text-sm flex items-center">
                    <i class="fas fa-layer-group text-blue-500 mr-2"></i>
                    Subcategories
                </h4>
                <span class="text-xs text-gray-500 bg-blue-100 text-blue-800 px-2 py-1 rounded-full">{{ $subcategories->count() }}</span>
            </div>
            <div class="space-y-1">
                <a href="{{ route('search', array_merge(request()->query(), ['subcategory' => null])) }}"
                   class="filter-option {{ !$subcategoryId ? 'active' : '' }}">
                    <div class="flex items-center">
                        <i class="fas fa-list text-gray-400 mr-2 w-4"></i>
                        <span>All in Category</span>
                    </div>
                    @if(!$subcategoryId)
                        <i class="fas fa-check text-orange-500"></i>
                    @endif
                </a>

                @foreach($subcategories as $subcategory)
                    <a href="{{ route('search', array_merge(request()->query(), ['subcategory' => $subcategory->id])) }}"
                       class="filter-option {{ $subcategoryId == $subcategory->id ? 'active' : '' }}">
                        <div class="flex items-center">
                            <i class="fas fa-tag text-gray-400 mr-2 w-4"></i>
                            <span>{{ $subcategory->name }}</span>
                        </div>
                        @if($subcategoryId == $subcategory->id)
                            <i class="fas fa-check text-orange-500"></i>
                        @endif
                    </a>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Quick Filters Section with Modern Design -->
        <div class="filter-section p-3 border-b border-gray-100">
            <div class="flex items-center justify-between mb-3">
                <h4 class="font-semibold text-gray-800 text-sm flex items-center">
                    <i class="fas fa-bolt text-yellow-500 mr-2"></i>
                    Quick Filters
                </h4>
            </div>
            <div class="space-y-1">
                <a href="{{ route('search', array_merge(request()->query(), ['vegetarian' => $isVegetarian ? null : 1])) }}"
                   class="filter-option {{ $isVegetarian ? 'active' : '' }}">
                    <div class="flex items-center">
                        <i class="fas fa-leaf text-green-500 mr-2 w-4"></i>
                        <span>Vegetarian Only</span>
                    </div>
                    @if($isVegetarian)
                        <i class="fas fa-check text-orange-500"></i>
                    @endif
                </a>

                <a href="{{ route('search', array_merge(request()->query(), ['popular' => $isPopular ? null : 1])) }}"
                   class="filter-option {{ $isPopular ? 'active' : '' }}">
                    <div class="flex items-center">
                        <i class="fas fa-star text-yellow-500 mr-2 w-4"></i>
                        <span>Popular Items</span>
                    </div>
                    @if($isPopular)
                        <i class="fas fa-check text-orange-500"></i>
                    @endif
                </a>
            </div>
        </div>

        <!-- Cuisines Section with Modern Design -->
        @if($cuisines->count() > 0)
        <div class="filter-section p-3 border-b border-gray-100">
            <div class="flex items-center justify-between mb-3">
                <h4 class="font-semibold text-gray-800 text-sm flex items-center">
                    <i class="fas fa-globe text-purple-500 mr-2"></i>
                    Cuisines
                </h4>
                <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{{ $cuisines->count() }}</span>
            </div>
            <div class="space-y-1 max-h-32 overflow-y-auto">
                <a href="{{ route('search', array_diff_key(request()->query(), ['cuisine' => ''])) }}"
                   class="filter-option {{ !$cuisineId ? 'active' : '' }}">
                    <div class="flex items-center">
                        <i class="fas fa-utensils text-gray-400 mr-2 w-4"></i>
                        <span>All Cuisines</span>
                    </div>
                    @if(!$cuisineId)
                        <i class="fas fa-check text-orange-500"></i>
                    @endif
                </a>

                @foreach($cuisines as $cuisine)
                    <a href="{{ route('search', array_merge(request()->query(), ['cuisine' => $cuisine->id])) }}"
                       class="filter-option {{ $cuisineId == $cuisine->id ? 'active' : '' }}">
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt text-gray-400 mr-2 w-4"></i>
                            <span>{{ $cuisine->name }}</span>
                        </div>
                        @if($cuisineId == $cuisine->id)
                            <i class="fas fa-check text-orange-500"></i>
                        @endif
                    </a>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Clear Filters with Modern Design -->
        @if($query || $categoryId || $subcategoryId || $cuisineId || $isVegetarian || $isPopular)
        <div class="p-3">
            <a href="{{ route('search') }}"
               class="clear-filters-btn block w-full text-center py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-300 font-medium shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                <i class="fas fa-times mr-2"></i>
                Clear All Filters
            </a>
        </div>
        @endif
    </div>
</div>

<!-- Mobile Overlay (not needed since sidebar is always visible) -->
<!-- Mobile Filter Toggle Button (not needed since sidebar is always visible) -->

<div class="transition-all duration-300 search-main-content" id="main-content">
    <div class="container-mobile search-page-container">
    <!-- Simplified Food Items Section - Only Items Display -->
    <div class="food-items-container">
        @if($foodItems->count() > 0)
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 search-products-grid mb-8">
                @foreach($foodItems as $item)
                <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="relative">
                        @if($item->image)
                            <img src="{{ $item->image }}" alt="{{ $item->name }}" class="w-full h-32 object-cover">
                        @else
                            <div class="w-full h-32 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400"></i>
                            </div>
                        @endif
                        <div class="absolute top-1 left-1 flex space-x-1">
                            @if($item->is_vegetarian)
                                <span class="bg-green-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-leaf"></i>
                                </span>
                            @endif
                            @if($item->is_popular)
                                <span class="bg-yellow-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-star"></i>
                                </span>
                            @endif
                        </div>
                        @if($item->is_spicy)
                            <span class="absolute top-1 right-1 bg-red-500 text-white px-1 py-0.5 rounded text-xs">
                                <i class="fas fa-pepper-hot"></i>
                            </span>
                        @endif
                    </div>
                    <div class="p-3">
                        <h3 class="font-semibold text-sm mb-1">{{ $item->name }}</h3>
                        <p class="text-xs text-gray-600 mb-2">{{ $item->category->name }} • {{ $item->cuisine->name }}</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-sm font-bold text-orange-600">${{ number_format($item->price_per_unit, 2) }}</span>
                                @if($item->allow_bulk_order && $item->price_per_kg)
                                    <span class="text-xs text-gray-500 block">${{ number_format($item->price_per_kg, 2) }}/kg</span>
                                @endif
                            </div>
                            <div class="flex space-x-1">
                                <a href="{{ route('menu.food-item', $item->slug) }}" class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs hover:bg-gray-200 transition-colors">
                                    View
                                </a>
                                <button onclick="addToCart('food_item', {{ $item->id }})" class="bg-orange-600 text-white px-2 py-1 rounded text-xs hover:bg-orange-700 transition-colors">
                                    Add
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Food Items Pagination -->
            <div class="mb-8">
                {{ $foodItems->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-utensils text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600">No food items found matching your search criteria.</p>
                <a href="{{ route('search') }}" class="text-orange-600 hover:text-orange-800 mt-2 inline-block">Clear filters and browse all items</a>
            </div>
        @endif
    </div>
    </div>
</div>

@push('scripts')
<script>

    // Filter update function
    function updateFilter(param, value) {
        const url = new URL(window.location);
        if (value && value !== '') {
            url.searchParams.set(param, value);
        } else {
            url.searchParams.delete(param);
        }
        window.location.href = url.toString();
    }

    // Advanced Filter Sidebar functionality (always visible now)
    function initializeFilterSidebar() {
        const sidebar = document.getElementById('filter-sidebar');
        if (sidebar) {
            // Ensure sidebar is always visible
            sidebar.classList.remove('-translate-x-full');
            sidebar.classList.add('translate-x-0');
        }
    }

    // Function to calculate and set header height
    function calculateHeaderHeight() {
        const header = document.querySelector('header');
        if (header) {
            const headerHeight = header.offsetHeight;
            document.documentElement.style.setProperty('--header-height', headerHeight + 'px');

            // Position the quick filters slider directly below the header
            const quickFiltersSlider = document.getElementById('quick-filters-slider');
            if (quickFiltersSlider) {
                quickFiltersSlider.style.top = headerHeight + 'px';
            }

            // Update sidebar position to account for both header and slider
            const sidebar = document.getElementById('filter-sidebar');
            if (sidebar) {
                const sliderHeight = quickFiltersSlider ? quickFiltersSlider.offsetHeight : 80;
                sidebar.style.top = (headerHeight + sliderHeight) + 'px';
                sidebar.style.height = `calc(100vh - ${headerHeight + sliderHeight}px - 80px)`;
            }
        }
    }

    // Initialize filter sidebar and page
    document.addEventListener('DOMContentLoaded', function() {
        // Calculate header height first
        calculateHeaderHeight();

        // Initialize filter sidebar (always visible)
        initializeFilterSidebar();

        // Add search page class to body for specific styling
        document.body.classList.add('search-page');

        // Ensure sticky footer is always visible
        ensureStickyFooterVisibility();

        // Recalculate on window resize
        window.addEventListener('resize', calculateHeaderHeight);
    });

    // Function to ensure sticky footer is always visible
    function ensureStickyFooterVisibility() {
        const bottomNav = document.querySelector('.bottom-nav');
        const sidebar = document.getElementById('filter-sidebar');

        if (bottomNav && sidebar) {
            // Ensure bottom nav has higher z-index than sidebar
            bottomNav.style.zIndex = '60';
            sidebar.style.zIndex = '30';

            // Add some extra bottom padding to body to ensure footer is always accessible
            document.body.style.paddingBottom = '80px';
        }
    }
</script>
@endpush
</div>
@endsection
