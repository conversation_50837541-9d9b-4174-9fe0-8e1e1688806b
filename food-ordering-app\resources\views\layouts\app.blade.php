<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Food Ordering App')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Bootstrap CSS (for dropdown functionality) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom Styles -->
    <style>
        /* Mobile-first responsive design */
        .container-mobile {
            max-width: 100%;
            padding: 0 1rem;
        }

        @media (min-width: 640px) {
            .container-mobile {
                max-width: 640px;
                margin: 0 auto;
            }
        }

        @media (min-width: 768px) {
            .container-mobile {
                max-width: 768px;
            }
        }

        @media (min-width: 1024px) {
            .container-mobile {
                max-width: 1024px;
            }
        }

        /* Sticky bottom navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 0.75rem 0;
        }

        /* Profile dropdown */
        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-radius: 0.5rem;
            min-width: 200px;
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            transition: all 0.2s;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #f97316;
        }

        /* Mobile profile menu */
        .mobile-profile-menu {
            position: fixed;
            bottom: 80px;
            right: 1rem;
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 50;
            display: none;
        }

        /* Food item cards */
        .food-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .food-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Filter buttons */
        .filter-btn {
            transition: all 0.2s;
        }

        .filter-btn.active {
            background: #3b82f6;
            color: white;
        }

        /* Loading spinner */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Advanced Filter Sidebar Styles */
        #filter-sidebar {
            backdrop-filter: blur(10px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-right: 2px solid #e2e8f0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #filter-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(249, 115, 22, 0.05) 0%, rgba(239, 68, 68, 0.05) 100%);
            pointer-events: none;
        }

        #filter-sidebar a {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        #filter-sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
            transition: left 0.5s;
        }

        #filter-sidebar a:hover {
            transform: translateX(3px);
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
            border-radius: 6px;
        }

        #filter-sidebar a:hover::before {
            left: 100%;
        }

        /* Enhanced sidebar header */
        #filter-sidebar .sidebar-header {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
            position: relative;
            overflow: hidden;
        }

        #filter-sidebar .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        /* Mobile filter toggle animation */
        #mobile-filter-toggle {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .8;
            }
        }

        /* Ensure bottom navigation stays above sidebar */
        .bottom-nav {
            z-index: 60 !important; /* Higher than sidebar z-index */
        }

        /* Enhanced Responsive Sidebar - Always show with 1/6 width and proper spacing */
        @media (max-width: 767px) {
            #filter-sidebar {
                width: 16.666667vw; /* 1/6 of viewport width */
                min-width: 120px; /* Minimum width for usability */
                max-width: 150px; /* Maximum width to prevent overflow */
                transform: translateX(0); /* Always visible */
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                border-right: 1px solid #e5e7eb;
                background: #ffffff;
                z-index: 30;
                top: calc(120px + 80px) !important; /* Account for filter nav header + sticky slider */
                height: calc(100vh - 120px - 80px) !important;
            }

            #main-content {
                margin-left: calc(16.666667vw + 0.5rem) !important; /* Match sidebar width with gap */
                padding-right: 0.5rem;
                transition: margin-left 0.3s ease;
            }

            #mobile-filter-toggle {
                display: none; /* Hide toggle button since sidebar is always visible */
            }

            /* Optimized text sizing for mobile sidebar */
            #filter-sidebar h3 {
                font-size: 0.75rem;
                line-height: 1.2;
                font-weight: 600;
            }

            #filter-sidebar h4 {
                font-size: 0.625rem;
                line-height: 1.2;
                font-weight: 500;
            }

            #filter-sidebar a {
                font-size: 0.625rem;
                padding: 0.5rem;
                line-height: 1.2;
            }

            #filter-sidebar p {
                font-size: 0.625rem;
                line-height: 1.2;
            }

            #filter-sidebar .filter-option {
                padding: 0.5rem;
                font-size: 0.625rem;
                line-height: 1.2;
            }

            #filter-sidebar .filter-section {
                padding: 0.75rem;
            }

            /* Ensure products are never hidden behind sidebar */
            .search-page .search-products-grid {
                margin-left: 0;
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            /* Adjust container padding for mobile only when sidebar is present */
            .search-page .container-mobile:not(.search-page-container) {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            /* Ensure minimum spacing on very small screens */
            @media (max-width: 480px) {
                #filter-sidebar {
                    min-width: 100px;
                    max-width: 120px;
                }

                #main-content {
                    margin-left: calc(100px + 0.5rem) !important;
                }

                #filter-sidebar h3 {
                    font-size: 0.625rem;
                }

                #filter-sidebar h4 {
                    font-size: 0.5rem;
                }

                #filter-sidebar a,
                #filter-sidebar .filter-option {
                    padding: 0.375rem;
                    font-size: 0.5rem;
                }
            }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            #filter-sidebar {
                width: 16.666667vw; /* 1/6 of viewport width */
                min-width: 140px;
                max-width: 180px;
                transform: translateX(0); /* Always visible */
                top: calc(120px + 80px) !important; /* Account for filter nav header + sticky slider */
                height: calc(100vh - 120px - 80px) !important;
            }

            #main-content {
                margin-left: 16.666667vw !important; /* Match sidebar width */
                min-margin-left: 140px !important;
                max-margin-left: 180px !important;
            }

            #mobile-filter-toggle {
                display: none; /* Hide toggle button */
            }

            /* Adjust container padding for tablet only when sidebar is present */
            .search-page .container-mobile:not(.search-page-container) {
                padding-left: calc(16.666667vw + 1rem);
                padding-right: 1rem;
            }
        }

        @media (min-width: 1024px) {
            #filter-sidebar {
                width: 16.666667vw; /* 1/6 of viewport width */
                min-width: 160px;
                max-width: 220px;
                transform: translateX(0);
                top: calc(120px + 80px) !important; /* Account for filter nav header + sticky slider */
                height: calc(100vh - 120px - 80px) !important;
            }

            #mobile-filter-toggle {
                display: none;
            }

            #main-content {
                margin-left: 16.666667vw !important; /* Match sidebar width */
                min-margin-left: 160px !important;
                max-margin-left: 220px !important;
            }

            /* Adjust container padding for desktop only when sidebar is present */
            .search-page .container-mobile:not(.search-page-container) {
                padding-left: calc(16.666667vw + 1rem);
                padding-right: 1rem;
            }
        }

        /* Ensure products grid doesn't get hidden */
        .food-card {
            position: relative;
            z-index: 1;
        }

        /* Enhanced scrollbar for sidebar */
        #filter-sidebar::-webkit-scrollbar {
            width: 4px;
        }

        #filter-sidebar::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
        }

        #filter-sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #f97316, #ea580c);
            border-radius: 2px;
        }

        #filter-sidebar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #ea580c, #dc2626);
        }

        /* Advanced Filter Navigation Header Styles */
        .filter-nav-container-header {
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            border-bottom: 1px solid #e5e7eb;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .filter-nav-container-header:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Filter Rows Container for Header */
        .filter-nav-container-header .filter-rows {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            padding: 1rem 0;
        }

        .filter-nav-container-header .filter-row {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .filter-nav-container-header .filter-row-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            min-width: 80px;
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        /* Enhanced Premium Filter Buttons for Header - More Focus */
        .filter-nav-container-header .premium-filter-btn {
            background: linear-gradient(135deg, #f3f4f6 0%, #ffffff 100%);
            color: #374151;
            border: 3px solid #e5e7eb;
            border-radius: 30px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 700;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            flex: 1;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .filter-nav-container-header .premium-filter-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .filter-nav-container-header .premium-filter-btn:hover::before {
            left: 100%;
        }

        .filter-nav-container-header .premium-filter-btn:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border-color: #ff6b35;
        }

        .filter-nav-container-header .premium-filter-btn.premium {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #92400e;
            border-color: #fbbf24;
        }

        .filter-nav-container-header .premium-filter-btn.premium:hover {
            background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
        }

        .filter-nav-container-header .premium-filter-btn.classic {
            background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
            color: white;
            border-color: #4f46e5;
        }

        .filter-nav-container-header .premium-filter-btn.classic:hover {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
        }

        .filter-nav-container-header .premium-filter-btn.delux {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            color: white;
            border-color: #8b5cf6;
        }

        .filter-nav-container-header .premium-filter-btn.delux:hover {
            background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
        }

        .filter-nav-container-header .premium-filter-btn.active {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Compact Diet Filter Buttons for Header */
        .filter-nav-container-header .diet-filter-btn {
            background: linear-gradient(135deg, #f3f4f6 0%, #ffffff 100%);
            color: #374151;
            border: 2px solid #e5e7eb;
            border-radius: 15px;
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            min-height: 40px;
        }

        .filter-nav-container-header .diet-filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .filter-nav-container-header .diet-filter-btn.veg {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-color: #10b981;
        }

        .filter-nav-container-header .diet-filter-btn.veg:hover {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .filter-nav-container-header .diet-filter-btn.non-veg {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border-color: #ef4444;
        }

        .filter-nav-container-header .diet-filter-btn.non-veg:hover {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        .filter-nav-container-header .diet-filter-btn.active {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-width: 3px;
        }

        /* Search Bar for Header */
        .filter-nav-container-header .search-input {
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
            border: 2px solid #e5e7eb;
            border-radius: 15px;
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            min-height: 40px;
            flex: 1;
            max-width: 200px;
        }

        .filter-nav-container-header .search-input:focus {
            border-color: #f59e0b;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
            background: #ffffff;
        }

        .filter-nav-container-header .search-input::placeholder {
            color: #9ca3af;
            font-size: 0.75rem;
        }

        /* Filter Icons for Header */
        .filter-nav-container-header .filter-icon {
            font-size: 1rem;
            margin-right: 0.25rem;
        }

        /* Responsive Design for Filter Nav Header */
        @media (max-width: 768px) {
            /* Aggressive mobile gap removal */
            header {
                margin: 0 !important;
                padding: 0 !important;
                border-bottom: none !important;
                margin-bottom: 0 !important;
                padding-bottom: 0 !important;
            }

            .filter-nav-container-header {
                margin: 0 !important;
                padding: 0 !important;
                border-bottom: none !important;
                position: relative;
                z-index: 41;
                margin-bottom: 0 !important;
                padding-bottom: 0 !important;
            }

            .filter-nav-container-header .filter-rows {
                padding: 0.75rem 0;
                gap: 0.75rem;
                margin-bottom: 0;
            }

            .filter-nav-container-header .filter-row {
                gap: 0.5rem;
                flex-wrap: nowrap; /* Prevent wrapping to keep buttons in rows */
            }

            .filter-nav-container-header .filter-row-label {
                font-size: 0.75rem;
                min-width: 50px;
                flex-shrink: 0;
            }

            .filter-nav-container-header .premium-filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.7rem;
                flex: 1;
                min-width: 0; /* Allow buttons to shrink */
                white-space: nowrap;
            }

            .filter-nav-container-header .diet-filter-btn {
                padding: 0.625rem 1rem;
                font-size: 0.75rem;
                flex: 0 0 auto;
                min-width: 0; /* Allow buttons to shrink */
                white-space: nowrap;
            }

            .filter-nav-container-header .search-input {
                max-width: 120px;
                font-size: 0.625rem;
                padding: 0.375rem 0.75rem;
                min-height: 32px;
            }

            .filter-nav-container-header .search-btn {
                padding: 0.375rem;
                font-size: 0.75rem;
                min-height: 32px;
                min-width: 32px;
            }
        }

        @media (max-width: 480px) {
            .filter-nav-container-header {
                margin-bottom: 0 !important;
                padding-bottom: 0 !important;
                border-bottom: 1px solid #e5e7eb;
            }

            .filter-nav-container-header .filter-rows {
                padding: 0.5rem 0;
                gap: 0.75rem;
                margin-bottom: 0;
            }

            .filter-nav-container-header .filter-row {
                flex-direction: row; /* Keep horizontal layout */
                align-items: center;
                gap: 0.375rem;
                flex-wrap: nowrap; /* Prevent wrapping */
            }

            .filter-nav-container-header .filter-row-label {
                font-size: 0.625rem;
                min-width: 40px;
                flex-shrink: 0;
            }

            .filter-nav-container-header .premium-filter-btn {
                padding: 0.375rem 0.5rem;
                font-size: 0.625rem;
                flex: 1;
                min-width: 0;
                border-radius: 20px;
            }

            .filter-nav-container-header .diet-filter-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.625rem;
                flex: 0 0 auto;
                min-width: 0;
                border-radius: 15px;
            }

            .filter-nav-container-header .search-input {
                max-width: 100px;
                font-size: 0.5rem;
                padding: 0.25rem 0.5rem;
                min-height: 28px;
            }

            .filter-nav-container-header .search-btn {
                padding: 0.25rem;
                font-size: 0.625rem;
                min-height: 28px;
                min-width: 28px;
            }

            /* Ensure icons are smaller on mobile */
            .filter-nav-container-header .premium-filter-btn i,
            .filter-nav-container-header .diet-filter-btn i {
                font-size: 0.75rem;
                margin-right: 0.25rem;
            }
        }

        /* Extra small mobile devices */
        @media (max-width: 360px) {
            .filter-nav-container-header {
                margin-bottom: 0 !important;
                padding-bottom: 0 !important;
                border-bottom: 1px solid #e5e7eb;
            }

            .filter-nav-container-header .filter-rows {
                margin-bottom: 0;
                padding: 0.375rem 0;
            }

            .filter-nav-container-header .filter-row-label {
                font-size: 0.5rem;
                min-width: 35px;
            }

            .filter-nav-container-header .premium-filter-btn {
                padding: 0.25rem 0.375rem;
                font-size: 0.5rem;
                letter-spacing: 0.25px;
            }

            .filter-nav-container-header .diet-filter-btn {
                padding: 0.375rem 0.5rem;
                font-size: 0.5rem;
            }

            .filter-nav-container-header .premium-filter-btn i,
            .filter-nav-container-header .diet-filter-btn i {
                font-size: 0.625rem;
                margin-right: 0.125rem;
            }
        }
    </style>

    @stack('styles')
</head>
<body class="bg-gray-50 pb-20">
    <!-- Advanced Filter Navigation Header -->
    <header class="bg-white shadow-sm sticky top-0 z-40" style="margin-bottom: 0; padding-bottom: 0;">
        <div class="container-mobile">
            <!-- Advanced Filter Navigation Bar (2 Rows) -->
            <div class="filter-nav-container-header" style="margin-bottom: 0; padding-bottom: 0;">
                <div class="filter-rows">
                    <!-- First Row: Premium, Classic, Delux -->
                    <div class="filter-row">
                        <div class="filter-row-label">
                            <i class="fas fa-crown filter-icon text-yellow-500"></i>
                            <span>Tier:</span>
                        </div>
                        <button class="premium-filter-btn premium" onclick="togglePremiumFilter('premium')" id="premium-btn">
                            <i class="fas fa-gem"></i>
                            Premium
                        </button>
                        <button class="premium-filter-btn classic" onclick="togglePremiumFilter('classic')" id="classic-btn">
                            <i class="fas fa-star"></i>
                            Classic
                        </button>
                        <button class="premium-filter-btn delux" onclick="togglePremiumFilter('delux')" id="delux-btn">
                            <i class="fas fa-crown"></i>
                            Delux
                        </button>
                    </div>

                    <!-- Second Row: Veg, Non-Veg, Search -->
                    <div class="filter-row">
                        <div class="filter-row-label">
                            <i class="fas fa-utensils filter-icon text-green-500"></i>
                            <span>Diet:</span>
                        </div>
                        <button class="diet-filter-btn veg {{ request()->routeIs('search') && request('vegetarian') ? 'active' : '' }}" onclick="toggleDietFilter('veg')" id="veg-btn">
                            <i class="fas fa-leaf"></i>
                            Vegetarian
                        </button>
                        <button class="diet-filter-btn non-veg" onclick="toggleDietFilter('non-veg')" id="non-veg-btn">
                            <i class="fas fa-drumstick-bite"></i>
                            Non-Vegetarian
                        </button>
                        <div class="flex items-center gap-2 flex-1">
                            <input type="text"
                                   class="search-input"
                                   placeholder="Search food items..."
                                   id="header-search"
                                   onkeypress="handleHeaderSearch(event)"
                                   value="{{ request('query') }}">
                            <button onclick="performHeaderSearch()" class="search-btn bg-orange-500 hover:bg-orange-600 text-white p-2 rounded-lg transition-colors">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen" style="margin: 0; padding: 0;">
        @yield('content')
    </main>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="container-mobile">
            <div class="flex justify-around items-center">
                <a href="{{ route('home') }}" class="flex flex-col items-center text-gray-600 hover:text-orange-600 transition-colors {{ request()->routeIs('home') ? 'text-orange-600' : '' }}">
                    <i class="fas fa-home text-lg mb-1"></i>
                    <span class="text-xs">Home</span>
                </a>

                <a href="{{ route('menu.index') }}" class="flex flex-col items-center text-gray-600 hover:text-orange-600 transition-colors {{ request()->routeIs('menu.*') ? 'text-orange-600' : '' }}">
                    <i class="fas fa-utensils text-lg mb-1"></i>
                    <span class="text-xs">Menu</span>
                </a>

                <a href="{{ route('catering.index') }}" class="flex flex-col items-center text-gray-600 hover:text-orange-600 transition-colors {{ request()->routeIs('catering.*') ? 'text-orange-600' : '' }}">
                    <i class="fas fa-concierge-bell text-lg mb-1"></i>
                    <span class="text-xs">Catering</span>
                </a>

                <a href="{{ route('cart.index') }}" class="flex flex-col items-center text-gray-600 hover:text-orange-600 transition-colors {{ request()->routeIs('cart.*') ? 'text-orange-600' : '' }}">
                    <div class="relative">
                        <i class="fas fa-shopping-cart text-lg mb-1"></i>
                        <span id="cart-badge" class="absolute -top-2 -right-2 bg-orange-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                    </div>
                    <span class="text-xs">Cart</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Mobile Profile Menu -->
    @auth
        <div id="mobileProfileMenu" class="mobile-profile-menu">
            <div class="p-4 border-b">
                <h6 class="font-semibold text-gray-800">{{ auth()->user()->name }}</h6>
                <small class="text-gray-500">{{ auth()->user()->mobile }}</small>
            </div>
            <div class="py-2">
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-user me-3 text-orange-600"></i>
                    My Profile
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-receipt me-3 text-orange-600"></i>
                    My Orders
                </a>
                <a href="{{ route('cart.index') }}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-shopping-cart me-3 text-orange-600"></i>
                    My Cart
                </a>
                <hr class="my-2">
                <form method="POST" action="{{ route('logout') }}" class="block">
                    @csrf
                    <button type="submit" class="flex items-center w-full px-4 py-3 text-red-600 hover:bg-red-50 transition-colors">
                        <i class="fas fa-sign-out-alt me-3"></i>
                        Logout
                    </button>
                </form>
            </div>
        </div>
    @endauth

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-20 right-4 z-50 space-y-2"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // CSRF Token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Mobile profile menu toggle
        function toggleMobileProfile() {
            const menu = document.getElementById('mobileProfileMenu');
            if (menu.style.display === 'none' || menu.style.display === '') {
                menu.style.display = 'block';
            } else {
                menu.style.display = 'none';
            }
        }

        // Function to show toast notification
        function showToast(message, type = 'success') {
            const toast = $(`
                <div class="toast bg-${type === 'success' ? 'green' : 'red'}-500 text-white px-4 py-2 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300">
                    ${message}
                </div>
            `);

            $('#toast-container').append(toast);

            setTimeout(() => {
                toast.removeClass('translate-x-full');
            }, 100);

            setTimeout(() => {
                toast.addClass('translate-x-full');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        }

        // Update cart count badge
        function updateCartCount() {
            $.get('{{ route("cart.count") }}', function(data) {
                const cartBadge = $('#cart-badge');
                if (data.cart_count > 0) {
                    cartBadge.text(data.cart_count).removeClass('hidden');
                } else {
                    cartBadge.addClass('hidden');
                }
            });
        }

        // Add to cart function (updated to show cart count)
        function addToCart(itemType, itemId, orderType = 'unit') {
            const quantity = parseInt($('#quantity').val()) || 1;
            const unitWeight = parseFloat($('#unit_weight').val()) || null;
            const specialInstructions = $('#special_instructions').val() || '';

            $.post('{{ route("cart.add") }}', {
                item_type: itemType,
                item_id: itemId,
                quantity: quantity,
                order_type: orderType,
                unit_weight: unitWeight,
                special_instructions: specialInstructions
            }, function(data) {
                if (data.success) {
                    showToast(data.message);
                    updateCartCount(); // Update cart count badge
                }
            }).fail(function(xhr) {
                const error = xhr.responseJSON?.error || 'Error adding item to cart';
                showToast(error, 'error');
            });
        }

        // Initialize on page load
        $(document).ready(function() {
            // Initialize cart count
            updateCartCount();

            // Close mobile profile menu when clicking outside
            $(document).click(function(event) {
                const menu = $('#mobileProfileMenu');
                const target = $(event.target);

                if (!target.closest('#mobileProfileMenu').length && !target.closest('[onclick="toggleMobileProfile()"]').length) {
                    menu.hide();
                }
            });
        });

        // Premium Filter Toggle Functionality
        let activePremiumFilter = null;

        function togglePremiumFilter(filterType) {
            // Remove active class from all premium buttons
            document.querySelectorAll('.premium-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            const button = document.getElementById(filterType + '-btn');

            // If clicking the same filter, deactivate it
            if (activePremiumFilter === filterType) {
                activePremiumFilter = null;
                // Add visual feedback for deactivation
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            } else {
                // Activate new filter
                activePremiumFilter = filterType;
                button.classList.add('active');

                // Add visual feedback for activation
                button.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            }

            // Here you can add logic to filter items based on premium tier
            console.log('Premium filter:', activePremiumFilter);

            // Add ripple effect
            createRippleEffect(button);
        }

        // Diet Filter Toggle Functionality
        let activeDietFilter = null;

        function toggleDietFilter(filterType) {
            // Remove active class from all diet buttons
            document.querySelectorAll('.diet-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            const button = document.getElementById(filterType + '-btn');

            // If clicking the same filter, deactivate it
            if (activeDietFilter === filterType) {
                activeDietFilter = null;
                // Add visual feedback for deactivation
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            } else {
                // Activate new filter
                activeDietFilter = filterType;
                button.classList.add('active');

                // Add visual feedback for activation
                button.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            }

            // Here you can add logic to filter items based on diet preference
            console.log('Diet filter:', activeDietFilter);

            // Add ripple effect
            createRippleEffect(button);

            // For vegetarian filter, you can integrate with existing functionality
            if (filterType === 'veg') {
                // Redirect to vegetarian filter
                const url = new URL(window.location);
                if (activeDietFilter === 'veg') {
                    url.searchParams.set('vegetarian', '1');
                } else {
                    url.searchParams.delete('vegetarian');
                }
                // Uncomment to enable actual filtering
                // window.location.href = url.toString();
            }
        }

        // Create ripple effect for buttons
        function createRippleEffect(button) {
            const ripple = document.createElement('span');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = rect.width / 2;
            const y = rect.height / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (x - size / 2) + 'px';
            ripple.style.top = (y - size / 2) + 'px';
            ripple.classList.add('ripple');

            // Add ripple styles
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.6)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.pointerEvents = 'none';

            button.style.position = 'relative';
            button.style.overflow = 'hidden';
            button.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // Add ripple animation keyframes
        const rippleStyle = document.createElement('style');
        rippleStyle.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(rippleStyle);

        // Header Search Functionality
        function handleHeaderSearch(event) {
            if (event.key === 'Enter') {
                performHeaderSearch();
            }
        }

        function performHeaderSearch() {
            const searchInput = document.getElementById('header-search');
            const query = searchInput.value.trim();

            if (query) {
                // Redirect to search page with query
                const url = new URL(window.location.origin + '/search');
                url.searchParams.set('query', query);
                window.location.href = url.toString();
            } else {
                // If empty, redirect to search page without query
                window.location.href = window.location.origin + '/search';
            }
        }
    </script>

    @stack('scripts')
</body>
</html>
